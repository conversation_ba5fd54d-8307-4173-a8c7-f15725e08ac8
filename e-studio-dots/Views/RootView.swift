//
//  RootView.swift
//  e-studio-dots
//
//  Created by Arno on 2025/7/15.
//

import SwiftUI

struct RootView: View {
    @StateObject private var authManager = AuthenticationManager()
    
    var body: some View {
        Group {
            if authManager.isSignedIn {
                UserInfoView(authManager: authManager)
            } else {
                LoginView()
                    .environmentObject(authManager)
            }
        }
        .animation(.easeInOut(duration: 0.3), value: authManager.isSignedIn)
    }
}

#Preview {
    RootView()
} 